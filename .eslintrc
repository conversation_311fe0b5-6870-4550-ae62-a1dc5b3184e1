{
  "extends": [
    "airbnb",
    "prettier",
    "prettier/react"
  ],
  "parser": "babel-eslint",
  "parserOptions": {
    "ecmaVersion": 2017,
    "ecmaFeatures": {
      "experimentalObjectRestSpread": true,
      "impliedStrict": true,
      "classes": true
    }
  },
  "env": {
    "browser": true,
    "node": true,
    "jquery": false
  },
  "overrides": [
    {
      "files": [
        "cypress/integration/*"
      ],
      "rules": {
        "no-undef": "off",
      }
    }
  ],
  "settings": {
    "import/resolver": {
      "node": {
        "paths": [
          "src",
          "src/components",
          "src/images",
          "src/actions"
        ]
      }
    },
  },
  "rules": {
    "template-curly-spacing": "off",
    "react/jsx-props-no-spreading": "off",
    "no-unused-vars": [
      1,
      {
        "argsIgnorePattern": "res|next|stage|^err|on|config"
      }
    ],
    "arrow-body-style": [
      2,
      "as-needed"
    ],
    "no-param-reassign": [
      2,
      {
        "props": false
      }
    ],
    "no-unused-expressions": [
      1,
      {
        "allowTaggedTemplates": true
      }
    ],
    "camelcase": "off",
    "no-console": 0,
    "no-use-before-define": 0,
    "no-underscore-dangle": 0,
    "linebreak-style": 0,
    "consistent-return": 0,
    "import": 0,
    "func-names": 0,
    "import/no-extraneous-dependencies": 0,
    "import/prefer-default-export": 0,
    "space-before-function-paren": 0,
    "import/extensions": 0,
    "import/named": 0,
    "react/no-danger": 0,
    "react/display-name": 1,
    "react/react-in-jsx-scope": 0,
    "react/forbid-prop-types": 1,
    "react/prop-types": 0,
    "react/prefer-stateless-function": 0,
    "react/no-unescaped-entities": 0,
    "jsx-a11y/accessible-emoji": 0,
    "jsx-a11y/click-events-have-key-events": 0,
    "jsx-a11y/no-static-element-interactions": 0,
    "jsx-a11y/label-has-for": 0,
    "jsx-a11y/label-has-associated-control": 1,
    "react-hooks/rules-of-hooks": "error",
    "react/jsx-filename-extension": [
      1,
      {
        "extensions": [
          ".js",
          ".jsx"
        ]
      }
    ],
    "quotes": [
      2,
      "single",
      {
        "avoidEscape": true,
        "allowTemplateLiterals": true
      }
    ],
    "prettier/prettier": [
      "error",
      {
        "trailingComma": "es5",
        "semi": true,
        "singleQuote": true,
        "printWidth": 120
      }
    ],
    "jsx-a11y/href-no-hash": "off",
    "jsx-a11y/anchor-is-valid": [
      "warn",
      {
        "aspects": [
          "invalidHref"
        ]
      }
    ]
  },
  "plugins": [
    "prettier",
    "react-hooks"
  ]
}