import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useRouter } from 'next/router';
import { wrapper } from 'utils/store';
import { toast } from 'react-toastify';

import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';
import { fetchTeamSupplier } from 'actions';
import getBaseURL from 'utils/getBaseURL';
import { ErrorComponent, Layout } from 'components/Common';
import useDocketStore from 'store/useDocketStore';
import { TeamOrderDocket, TeamOrderMenu, SupplierBanner } from 'components/SupplierShow';
import { supplierShowSEO } from 'utils/seo';
import yordar from 'api/yordar';
import { DYNAMIC_TEAM_ORDER_ENDPOINT } from 'api/endpoints';

const AttendeePage = (props) => {
  const { errors, redirect, user } = props;
  const forDash = user?.type === 'customer';
  if (errors) {
    return (
      <ErrorComponent
        text={errors.join('.')}
        redirect={redirect}
        redirectText="Back To Your Team Orders"
        hideHeaderAndFooter={!forDash}
        forDash={forDash}
      />
    );
  }
  return <AttendeeOrder {...props} />;
};

const AttendeeOrder = ({ listing, user, order, attendee }) => {
  const { title, description } = supplierShowSEO({ name: listing.name, description: listing.description });
  const router = useRouter();
  const {
    query: { menu_item_id: menuItemID },
  } = router;

  const { populateSessionOrder, populateTeamAttendee } = useDocketStore((state) => ({
    populateSessionOrder: state.populateSessionOrder,
    populateTeamAttendee: state.populateTeamAttendee,
  }));

  const listingRedux = useSelector((state) => state.suppliers.listing);

  useEffect(() => {
    populateSessionOrder(order);
    populateTeamAttendee(attendee);
  }, []);

  const forDash = user?.type === 'customer';

  useEffect(() => {
    if (listing.warnings) {
      listing.warnings.forEach((warning) => {
        toast.warning(warning, { ...defaultToastOptions, ...toastTypeOptions.success });
      });
    }
  }, []);

  return (
    <Layout
      seo={{
        title,
        description,
      }}
      forDash={forDash}
      customClass={forDash ? 'supplier-show' : ''}
      type="menu-dash"
      hideFooter
    >
      <div className={`supplier-show-heading-block ${forDash ? 'for-dash' : ''}`} />
      <div className={`supplier-show-container ${forDash ? 'for-dash' : ''}`}>
        <div className="supplier-show-wrapper">
          <div className="supplier-details-container">
            <SupplierBanner listing={listing} />
            <TeamOrderMenu listing={listingRedux.name ? listingRedux : listing} itemFromSearch={menuItemID} />
          </div>
          <TeamOrderDocket />
        </div>
      </div>
    </Layout>
  );
};

export const getServerSideProps = wrapper.getStaticProps(async ({ req, store, params }) => {
  const { getState, dispatch } = store;
  const cookies = req?.headers?.cookie;
  const host = req?.headers?.host;

  const { uuid } = params;
  try {
    const { data: payload } = await yordar.get(DYNAMIC_TEAM_ORDER_ENDPOINT(uuid), {
      withCredentials: true,
      headers: cookies ? { Cookie: cookies } : {},
      baseURL: getBaseURL({ reqHost: host, type: 'app' }),
    });
    const { order, attendee, ...supplier } = payload;
    await dispatch(fetchTeamSupplier(supplier, uuid));
    const state = getState();
    const { listing } = state.suppliers;
    return {
      props: {
        params,
        listing,
        order,
        attendee,
      },
    };
  } catch (error) {
    return {
      props: {
        ...(error?.response?.data?.errors && { errors: error.response.data.errors }),
        ...(error.response.data?.package_url && { redirect: error.response.data?.package_url }),
      },
    };
  }
});

export default AttendeePage;
