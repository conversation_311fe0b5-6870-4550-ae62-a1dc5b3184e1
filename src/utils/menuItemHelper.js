const allServingsSamePrice = (servings) => servings.every((serving) => serving.price === servings[0].price);

export const cardPriceDisplayValue = (price, servings) => {
  const displayPrice = servings?.length ? `$${servings[0].price}` : `$${price}`;
  if (!servings?.length || allServingsSamePrice(servings)) {
    return displayPrice;
  }
  return priceRange(servings);
};

export const truncateText = (description, truncateAt) => {
  if (description?.length > truncateAt) return `${description.slice(0, truncateAt)}...`;
  return description;
};

const priceRange = (servings) => {
  const prices = servings.map((serving) => serving.price);
  const minPrice = Math.min.apply(null, prices).toFixed(2);
  const maxPrice = Math.max.apply(null, prices).toFixed(2);
  return `$${minPrice}-$${maxPrice}`;
};

export const sectionHasNoSearchedItems = (menuItems, searchedMenuItem) =>
  searchedMenuItem && !menuItems.some((item) => item.name.toLowerCase().includes(searchedMenuItem.toLowerCase()));

// Map full dietary names to their short codes used in menu items
const dietaryNameToCodeMap = {
  'Gluten Free': 'GF',
  Vegetarian: 'V',
  Vegan: 'VE',
  'Dairy Free': 'DF',
  'Nut Free': 'NF',
  'Egg Free': 'EF',
  Halal: 'H',
  Kosher: 'K',
};

// Filter menu items by dietary restrictions
export const filterMenuItemsByDietaries = (menuItems, selectedDietaryFilters) => {
  if (!selectedDietaryFilters || selectedDietaryFilters.length === 0) {
    return menuItems;
  }

  // Convert selected dietary filter names to their corresponding codes
  const selectedDietaryCodes = selectedDietaryFilters
    .map((filterName) => dietaryNameToCodeMap[filterName])
    .filter(Boolean); // Remove any undefined values

  return menuItems.filter((item) => {
    // If no dietary codes to filter by, return all items
    if (selectedDietaryCodes.length === 0) return true;

    // Check if the item has dietaries array
    if (!item.dietaries || !Array.isArray(item.dietaries)) return false;

    // Check if the item has ALL selected dietary restrictions
    return selectedDietaryCodes.every((code) => item.dietaries.includes(code));
  });
};

// Filter menu sections by dietary restrictions
export const filterMenuSectionsByDietaries = (sections, selectedDietaryFilters) => {
  if (!selectedDietaryFilters || selectedDietaryFilters.length === 0) {
    return sections;
  }

  return sections
    .map((section) => ({
      ...section,
      menu_items: filterMenuItemsByDietaries(section.menu_items, selectedDietaryFilters),
    }))
    .filter((section) => section.menu_items.length > 0); // Only return sections that have items after filtering
};
