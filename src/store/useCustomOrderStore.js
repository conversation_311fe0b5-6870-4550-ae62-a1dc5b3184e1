import { create } from 'zustand';

const initialOrderLine = {
  id: null,
  quantity: null,
  note: '',
  category_id: null,
  custom_item: {
    name: '',
    description: '',
    baseline: null,
    baseline_inc_gst: null,
    cost: null,
    price: null,
    baseline_inc_markdown: false,
    is_gst_free: false,
    supplier_id: null,
    image: null,
  },
};

const initialModalState = {
  modalOpen: false,
  currentOrderLine: { ...initialOrderLine },
  isEditing: false,
  editingItemId: null,
  currentLocation: null,
  currentSupplier: null,
};

const useCustomOrderStore = create((set) => ({
  ...initialModalState,

  setModalOpen: (open) => set({ modalOpen: open }),

  newOrderLine: ({ location, supplier }) =>
    set({
      modalOpen: true,
      currentOrderLine: {
        ...initialOrderLine,
        category_id: supplier.category_id,
        custom_item: {
          ...initialOrderLine,
          supplier_id: supplier.id,
        },
      },
      isEditing: false,
      currentLocation: location,
      currentSupplier: supplier,
    }),

  editOrderLine: ({ orderLine, location, supplier }) =>
    set({
      modalOpen: true,
      currentOrderLine: orderLine,
      currentLocation: location,
      currentSupplier: supplier,
    }),

  newOrderLineFromMenu: ({ menuItem, location, supplier }) =>
    set({
      modalOpen: true,
      currentOrderLine: {
        ...initialOrderLine,
        category_id: supplier.category_id,
        custom_item: {
          ...initialOrderLine.custom_item,
          supplier_id: supplier.id,
          name: menuItem.name,
          description: menuItem.description,
          image: menuItem.image_id,
          baseline: menuItem.price,
          baseline_inc_markdown: true,
          servings_sizes: menuItem.serving_sizes,
        },
      },
      currentLocation: location,
      currentSupplier: supplier,
    }),

  cloneOrderLine: ({ orderLine, location, supplier }) =>
    set({
      modalOpen: true,
      currentOrderLine: {
        ...orderLine,
        id: null,
        image: null,
        custom_item: {
          ...orderLine.custom_item,
          image: null,
        },
      },
      currentLocation: location,
      currentSupplier: supplier,
    }),

  closeModal: () =>
    set((state) => ({
      ...state,
      modalOpen: false,
      currentOrderLine: { ...initialOrderLine },
    })),
}));

export default useCustomOrderStore;
