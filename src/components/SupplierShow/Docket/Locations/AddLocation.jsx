import { useState } from 'react';
import Image from 'next/image';

import useDocketStore from 'store/useDocketStore';

const LoadingDots = require('images/icons/three-dots.svg');

const AddLocation = ({ activeOrderID }) => {
  const [newLocation, setNewLocation] = useState(null);
  const [fetchingLocation, setFetchingLocation] = useState(false);
  const { setLocation } = useDocketStore((state) => ({
    setLocation: state.setLocation,
  }));

  const handleEnter = (event) => {
    if (event.which === 13 || event.keyCode === 13) {
      handleLocationAdd();
    }
  };

  const handleLocationAdd = () => {
    setFetchingLocation(true);
    setLocation({ orderID: activeOrderID, method: 'post', name: newLocation });
    setFetchingLocation(false);
    setNewLocation('');
  };

  if (fetchingLocation) {
    return (
      <div className="fetching-location">
        <Image src={LoadingDots} alt="loading indicator" />
      </div>
    );
  }

  return (
    <div className="add-new-location">
      <input
        placeholder="Service Point Name"
        value={newLocation}
        onChange={(e) => setNewLocation(e.target.value)}
        onKeyUp={handleEnter}
      />
      <button type="button" className="button tiny" onClick={handleLocationAdd}>
        Add
      </button>
    </div>
  );
};

export default AddLocation;
