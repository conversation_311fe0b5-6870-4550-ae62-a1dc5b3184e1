import React, { useRef, useEffect, useState } from 'react';

import useDocketStore from 'store/useDocketStore';

const EditLocation = ({ name, id, setChangingLocationID }) => {
  const locationInput = useRef(null);
  const [locationName, setLocationName] = useState(name);
  const { activeOrderID, setLocation } = useDocketStore((state) => ({
    activeOrderID: state.activeOrderID,
    setLocation: state.setLocation,
  }));

  useEffect(() => {
    locationInput.current.focus();
    locationInput.current.select();
  }, []);

  const handleEnter = (event) => {
    event.stopPropagation();
    if (event.which === 13 || event.keyCode === 13) {
      handleLocationUpdate(event);
    }
  };

  const handleLocationUpdate = (event) => {
    event.stopPropagation();
    setLocation({ id, name: locationName, method: 'put', orderID: activeOrderID });
    setChangingLocationID(null);
  };

  return (
    <>
      <input
        className="locations-edit__input"
        type="text"
        value={locationName}
        ref={locationInput}
        onClick={(e) => e.stopPropagation()}
        onKeyUp={handleEnter}
        onChange={(e) => {
          e.stopPropagation();
          setLocationName(e.target.value);
        }}
      />
      <div className="locations-edit__options">
        <span className="icon icon-checkmark" onClick={handleLocationUpdate} role="presentation" />
        <span
          className="icon icon-cancel"
          onClick={(e) => {
            e.stopPropagation();
            setChangingLocationID(null);
          }}
          role="presentation"
        />
      </div>
    </>
  );
};

export default EditLocation;
