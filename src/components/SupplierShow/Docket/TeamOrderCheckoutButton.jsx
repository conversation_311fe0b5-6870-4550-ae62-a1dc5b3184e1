import { useState } from 'react';

import useDocketStore from 'store/useDocketStore';
import { postAttendeeOrder } from 'utils/api';
import Modal from 'react-responsive-modal';

const TeamOrderCheckoutButton = ({ isActive }) => {
  const teamAttendee = useDocketStore((state) => state.teamAttendee);
  const [loading, setLoading] = useState(false);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalData, setModalData] = useState({});

  async function checkoutTeamAttendee() {
    setLoading(true);
    setModalData({});
    const data = await postAttendeeOrder(teamAttendee.uuid, teamAttendee?.team_order_level_id);
    if (data.success) {
      setModalOpen(true);
      setModalData({ title: 'Success', msg: 'Thanks! Your order has been submitted', link: data.redirect_url });
    } else if (data.errors) {
      setModalOpen(true);
      setModalData({ title: 'Team Order Error', msg: data.errors[0] });
    } else {
      alert('Something went wrong');
    }
    setLoading(false);
  }
  return (
    <>
      <button
        type="button"
        onClick={checkoutTeamAttendee}
        className={`docket-checkout-button button ${isActive ? '' : 'disabled'}`}
      >
        {loading ? 'Submitting...' : 'Checkout'}
      </button>
      <Modal
        open={modalOpen}
        center
        styles={{
          modal: { maxWidth: '500px', padding: 0, borderRadius: '10px' },
        }}
        showCloseIcon
        onClose={() => setModalOpen(false)}
      >
        <div className="menu-modal">
          <h3 className="menu-modal__heading">{modalData.title}</h3>
          {modalData.msg && <p>{modalData.msg}</p>}
          {modalData.link && (
            <a href={modalData.link} className="button">
              {modalData.link.includes('attendee-package') ? 'Back To Your Team Orders' : 'Home'}
            </a>
          )}
        </div>
      </Modal>
    </>
  );
};

export default TeamOrderCheckoutButton;
