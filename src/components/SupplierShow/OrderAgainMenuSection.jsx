import useModalStore from 'store/useModalStore';
import useMenuItemStore from 'store/useMenuItemStore';

const OrderAgainMenuSection = ({ recentOrders }) => {
  const setModalOpen = useModalStore((state) => state.setModalOpen);
  const setOrderAgain = useMenuItemStore((state) => state.setOrderAgain);

  return (
    <div className="menu-section" id="menu-section-recent-orders" data-title="Order Again">
      <h2 className="menu-section-title">Order Again</h2>
      <ul className="menu-section-items">
        {recentOrders.map((order) => (
          <li
            key={order.id}
            className="menu-section-item"
            onClick={() => {
              setModalOpen(true);
              setOrderAgain(order);
            }}
            role="presentation"
          >
            <div>
              <h4 style={{ marginBottom: '0' }}>{order.name}</h4>
              <p style={{ fontSize: '12px', color: 'grey', marginBottom: '4px', fontWeight: 'bold' }}>
                Last ordered for {order.delivery_date}
              </p>
            </div>
            <div className="menu-section-item-description-and-image">
              <div className="menu-section-item-description no-flex">
                {order.order_lines.slice(0, 6).map((orderLine) => (
                  <p key={orderLine.id}>{`${orderLine.quantity}x ${orderLine.name}`}</p>
                ))}
                {order.order_lines.length > 6 && (
                  <p>
                    <strong>{`+ ${order.order_lines.length - 6} more items`}</strong>
                  </p>
                )}
              </div>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default OrderAgainMenuSection;
