import { OperatorSVG } from 'components/Common';

const NumberButton = ({ menuItemValue, setMenuItemValue, operation, setShowQuantitySubceeded, minQuantity }) => {
  const handleMinQuantity = () => {
    if (!minQuantity || minQuantity === 1) return;
    if (menuItemValue < minQuantity && menuItemValue !== 0) {
      return setShowQuantitySubceeded(true);
    }
    return setShowQuantitySubceeded(false);
  };
  return (
    <div>
      <button
        type="button"
        className="menu-item-modal-button"
        onClick={() => {
          setMenuItemValue(menuItemValue);
          handleMinQuantity();
        }}
        onKeyDown={(e) => e.preventDefault()}
      >
        {operation === '+' ? <OperatorSVG sign="plus" /> : <OperatorSVG sign="minus" />}
      </button>
    </div>
  );
};

export default NumberButton;
