import { useContext, useRef } from 'react';

import { UserContext } from 'context/user';
import useActiveMenuSection from 'hooks/SupplierShow/useActiveMenuSection';
import { MenuItem, OrderAgainMenuSection } from 'components/SupplierShow';
import { useRouter } from 'next/router';
import useFilterStore from 'store/useFilterStore';
import { filterMenuSectionsByDietaries } from 'utils/menuItemHelper';
import { dietaryCategories } from 'static/categories';

const MenuSections = ({ sections, searchedMenuItem, recentOrders }) => {
  const { user } = useContext(UserContext);
  const {
    query: { mealUUID },
  } = useRouter();

  // Get dietary filters from the filter store
  const { filters } = useFilterStore((state) => ({
    filters: state.filters,
  }));

  // Filter out only dietary filters from all filters
  const selectedDietaryFilters = filters.filter((filter) => dietaryCategories.includes(filter));

  // Apply dietary filtering to sections
  const filteredSections = filterMenuSectionsByDietaries(sections, selectedDietaryFilters);

  const menuSectionRefs = useRef(new Array(filteredSections.length).fill(null));

  useActiveMenuSection(menuSectionRefs);

  return (
    <div className="menu-section-wrapper">
      <div className="menu-sections">
        {!!recentOrders?.length && <OrderAgainMenuSection recentOrders={recentOrders} />}

        {filteredSections.map((section, i) => {
          const showMealPlanTag =
            mealUUID &&
            section.categories.some((category) =>
              ['buffets', 'individually-boxed-meals', 'share-meals'].includes(category)
            );

          return (
            <div
              className="menu-section"
              id={`menu-section-${section.id}`}
              key={section.id}
              ref={(ref) => {
                menuSectionRefs.current[i] = ref;
              }}
              data-title={section.name}
            >
              <h2 className="menu-section-title">
                {section.name}
                {showMealPlanTag ? <span className="meal-plan-section-tag">Meal Plan Section</span> : ''}
              </h2>
              <ul className="menu-section-items">
                {section.menu_items.map((item) => (
                  <MenuItem key={item.id} item={item} searchedMenuItem={searchedMenuItem} user={user} />
                ))}
              </ul>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MenuSections;
