import Image from 'next/image';
import Link from 'next/link';

import { Layout } from 'components/Common';

const ErrorComponent = ({ text, redirect, redirectText, hideHeaderAndFooter, forDash }) => (
  <Layout
    seo={{ title: "Yordar | There's been an issue" }}
    hideHeaderAndFooter={hideHeaderAndFooter}
    forDash={forDash}
    customClass={forDash ? 'supplier-show' : ''}
  >
    <div className="error-container">
      <Image src="/error.png" alt="error-illustration" width={340} height={340} />
      <h2>{text}</h2>
      {!redirect && (
        <Link href="/">
          <a className="button">Go Back home</a>
        </Link>
      )}
      {redirect && (
        <Link href={redirect}>
          <a className="button">{redirectText}</a>
        </Link>
      )}
    </div>
  </Layout>
);

export default ErrorComponent;
