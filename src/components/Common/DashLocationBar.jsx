import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import PlacesAutocomplete, { geocodeByPlaceId } from 'react-places-autocomplete';

import useSearchedAddress from 'hooks/Common/useSearchedAddress';
import { CategoryContext } from 'context/category';
import { HostContext } from 'context/host';

const DashLocationBar = ({ disabled }) => {
  const [loading, setLoading] = useState(false);
  const [value, setValue] = useState('');
  const [address, setAddress] = useState('');
  const [isValid, setIsValid] = useState(false);

  const { locality, orderURL } = useContext(HostContext);
  const category = useContext(CategoryContext);

  const router = useRouter();
  const { quoteUUID } = router.query;

  useEffect(() => {
    if (address && value === address) {
      setIsValid(true);
    } else {
      setIsValid(false);
    }
  }, [value, address]);

  useSearchedAddress({ setValue, setAddress });

  const onSearchSubmit = async (val, placeId) => {
    try {
      const [result] = await geocodeByPlaceId(placeId);

      const { address_components: addressComponents } = result;
      // Get values from google places address components array
      const streetNumType = 'street_number';
      const streetNameType = 'route';
      const subLocalityType = 'sublocality';
      const suburbType = 'locality';
      const stateType = 'administrative_area_level_1';
      let suburb = addressComponents
        .find((comp) => comp.types.includes(subLocalityType))
        ?.short_name?.replace(/\s/g, '-');
      suburb ||= addressComponents.find((comp) => comp.types.includes(suburbType)).short_name.replace(/\s/g, '-');
      const state = addressComponents.find((comp) => comp.types.includes(stateType)).short_name;
      const streetNumber = addressComponents.find((comp) => comp.types.includes(streetNumType))?.long_name;
      const streetName = addressComponents.find((comp) => comp.types.includes(streetNameType))?.long_name;

      let streetAddress = null;

      if (streetName) {
        streetAddress = streetNumber ? `${streetNumber} ${streetName}` : streetName;
      }

      // Save values to state
      setLoading(true);
      setValue(val);

      localStorage.setItem('address', val);

      // Push user to app on address selection
      let searchURL = `${orderURL}/search/${category || 'office-catering'}/${state}/${suburb}`;

      // add params
      const params = [];
      if (streetAddress) params.push(`street_address=${streetAddress}`);
      if (quoteUUID) params.push(`quoteUUID=${quoteUUID}`);
      if (params.length) searchURL += `?${params.join('&')}`;

      window.location.href = searchURL;
    } catch (error) {
      console.log('Error fetching address', error);
    }
  };

  const handleChange = (val) => {
    setValue(val);
  };

  const handleFocus = () => {
    setValue('');
  };

  const handleBlur = () => {
    setValue(address || '');
  };

  const handleSelect = (val, placeId) => {
    setAddress(val);
    onSearchSubmit(val, placeId);
  };

  const searchOptions = {
    componentRestrictions: { country: locality },
    types: ['geocode'],
  };

  const label = 'Enter street address, suburb or city';

  return (
    <>
      <div className="searchbar dash-location-bar">
        <PlacesAutocomplete
          searchOptions={searchOptions}
          onChange={handleChange}
          onSelect={handleSelect}
          value={value || ''}
        >
          {({ getInputProps, suggestions, getSuggestionItemProps, loading: loadingSuggestions }) => (
            <>
              <input
                onFocus={handleFocus}
                {...getInputProps({
                  placeholder: label,
                  className: 'location-search-input',
                  onBlur: handleBlur,
                  disabled,
                })}
              />
              {(suggestions.length > 0 || loadingSuggestions) && (
                <div className="autocomplete-dropdown-container">
                  {loadingSuggestions && <div className="autocomplete-dropdown-item">Loading...</div>}
                  {suggestions.map((suggestion) => {
                    const className = `autocomplete-dropdown-item ${suggestion.active ? 'active' : ''} `;
                    return (
                      <div {...getSuggestionItemProps(suggestion, { className })}>
                        <span>{suggestion.description}</span>
                      </div>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </PlacesAutocomplete>
      </div>
    </>
  );
};

export default DashLocationBar;
