import { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import LogoDark from 'images/logo-dark.svg';
import Image from 'next/image';

import { CategoryContext } from 'context/category';
import useSidebarStore from 'store/useSidebarStore';
import { HostContext } from 'context/host';
import { UserContext } from 'context/user';

const DashSidebar = ({ isMealPlan }) => {
  const { user } = useContext(UserContext);
  const router = useRouter();
  const { category, state, suburb } = router.query;
  const categoryFromContext = useContext(CategoryContext);
  const { appURL } = useContext(HostContext);
  const { open } = useSidebarStore();

  const [geoLocation, setGeoLocation] = useState(null);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storedGeoLocation = localStorage.getItem('geo-location');
      if (storedGeoLocation) {
        setGeoLocation(JSON.parse(storedGeoLocation));
      }
    }
  }, []);

  const sideBarLinks = [
    {
      name: 'Orders',
      link: `${appURL}/c_profile`,
      icon: 'orders-list',
      nested: [
        { name: 'Upcoming Orders', link: `${appURL}/c_profile` },
        { name: 'Past Orders', link: `${appURL}/c_profile?show_past=true` },
        ...(user?.signed_in_as_admin ? [{ name: 'New Custom Order', link: `/checkout/custom` }] : []),
      ],
      isActive: (router.asPath.includes('edit') || router.asPath.includes('custom')) && !isMealPlan,
    },
    {
      name: 'Catering',
      link: `/search/office-catering/${geoLocation?.state || state}/${geoLocation?.suburb || suburb}`,
      icon: 'catering',
      isActive:
        (category === 'office-catering' || categoryFromContext === 'office-catering') &&
        !router.asPath.includes('/quotes') &&
        !router.query.mealUUID &&
        !isMealPlan &&
        !router.asPath.includes('/edit') &&
        !router.asPath.includes('/custom'),
    },
    {
      name: 'Pantry',
      link: `/search/office-snacks/${geoLocation?.state || state}/${geoLocation?.suburb || suburb}`,
      icon: 'snacks',
      isActive:
        (category === 'office-snacks' || categoryFromContext === 'office-snacks') &&
        !router.asPath.includes('/quotes') &&
        !router.query.mealUUID,
    },
    {
      name: 'Meal Plans',
      link: `${appURL}/c_profile/meal-plans`,
      icon: 'meal-plans',
      isActive: router.query.mealUUID || isMealPlan,
      nested: [
        { name: 'Individual Meals', link: `${appURL}/c_profile/new-team-order` },
        { name: 'Shared Meals', link: `${appURL}/c_profile/meal-plans` },
      ],
    },
    {
      name: 'Quotes',
      link: `${appURL}/c_profile?only_quotes=true`,
      icon: 'quotes-list',
      isActive: router.asPath.includes('/quotes'),
    },
    { name: 'My Suppliers', link: `${appURL}/c_profile/my_suppliers`, icon: 'with-favourite-heart' },
    {
      name: 'Billing',
      link: `${appURL}/c_profile/invoices`,
      icon: 'billing-list',
      nested: [
        { name: 'Invoices', link: `${appURL}/c_profile/invoices` },
        { name: 'Purchase Orders', link: `${appURL}/c_profile/purchase-orders` },
        { name: 'Credit Cards', link: `${appURL}/c_profile/payment-options` },
      ],
    },
    { name: 'Reports', link: `${appURL}/c_profile/reports`, icon: 'reports-list' },
    { name: 'Surveys', link: `${appURL}/c_profile/employee-surveys`, icon: 'surveys-list' },
    {
      name: 'Settings',
      link: `${appURL}/c_profile/account-and-billing`,
      icon: 'settings-list',
      nested: [
        { name: 'Account Details', link: `${appURL}/c_profile/account-and-billing` },
        { name: 'My Addresses', link: `${appURL}/c_profile/saved-addresses` },
        ...(user?.signed_in_as_admin
          ? [{ name: 'Customer Settings', link: `${appURL}/c_profile/customer-settings` }]
          : []),
        { name: 'Notification Preferences', link: `${appURL}/c_profile/notification-preferences` },
      ],
    },
  ];

  return (
    <div className={`customer-sticky-sidebar ${open ? 'mobile-open' : ''}`}>
      <aside className="customer-area-sidebar">
        <div>
          <div className="customer-area-sidebar__image">
            <Image src={LogoDark} width={95} />
          </div>
          <ul className="customer-area-sidebar__ul vertical">
            {sideBarLinks.map(({ name, icon, link, isActive, nested }) => (
              <SidebarLink key={name} name={name} icon={icon} link={link} isActive={isActive} nested={nested} />
            ))}
          </ul>
        </div>
        <div>
          <div className="personalised">
            <div className="personalised__tag circle-icon">
              {user.first_name[0]}
              {user.last_name[0]}
            </div>
            <div>
              <span className="personalised__name sidebar">
                {user.first_name} {user.last_name}
              </span>
            </div>
          </div>
        </div>
      </aside>
    </div>
  );
};

const SidebarLink = ({ name, icon, link, isActive, nested }) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleSubMenu = () => setIsOpen(!isOpen);

  return (
    <li className={`customer-area-sidebar__li ${isActive ? 'active' : ''} ${nested ? 'nested-list' : ''}`}>
      {!nested && (
        <a href={link} className={`customer-sidebar-link ${icon}`}>
          {name}
        </a>
      )}
      {nested && (
        <a
          className={`customer-sidebar-link ${icon} nested-link ${isOpen ? 'open' : ''} ${isActive ? 'active' : ''}`}
          onClick={toggleSubMenu}
        >
          {name}
        </a>
      )}
      {nested && isOpen && (
        <ul className="customer-area-sidebar__sub-menu">
          {nested.map(({ name: nestedName, link: nestedLink }) => (
            <li key={`nested-link-${nestedName}`} className="customer-area-sidebar__sub-item">
              <a href={nestedLink} className="customer-nested-link">
                {nestedName}
              </a>
            </li>
          ))}
        </ul>
      )}
    </li>
  );
};

export default DashSidebar;
