import { useState } from 'react';
import slugify from 'slugify';

const SingleSelectQuestion = ({ question, setAnswer }) => {
  const [selectedOption, setSelectedOption] = useState();

  const handleClick = (event) => {
    setSelectedOption(event.target.value);
    setAnswer((state) => ({ ...state, value: event.target.value }));
  };

  return (
    <div className="single-select-question">
      {question.options.map((option) => (
        <button
          type="button"
          key={slugify(option)}
          onClick={handleClick}
          className={`button ${selectedOption === option ? 'selected' : ''} single-select-answer`}
          value={option}
        >
          {option}
        </button>
      ))}
    </div>
  );
};

export default SingleSelectQuestion;
