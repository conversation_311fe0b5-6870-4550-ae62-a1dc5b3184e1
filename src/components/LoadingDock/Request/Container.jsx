import { useState } from 'react';
import { RequestForm } from 'components/LoadingDock';

import yordar from 'api/yordar';
import { DYNAMIC_LOADING_DOCK_ENDPOINT } from 'api/endpoints.js';

import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';

const approvedLoadingDockFields = ['id', 'code', 'customer_profile_id', 'saved_for_future', 'file_url'];

const Container = ({ order, customer, loadingDock }) => {
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (localLoadingDock) => {
    const sanitizedLoadingDock = {};
    approvedLoadingDockFields.forEach((field) => {
      if (localLoadingDock[field]) {
        sanitizedLoadingDock[field] = localLoadingDock[field];
      }
    });

    try {
      await yordar(DYNAMIC_LOADING_DOCK_ENDPOINT(order.uuid), {
        method: 'post',
        withCredentials: true,
        data: {
          loading_dock: sanitizedLoadingDock,
        },
      });
      toast.success('Loading Dock Set for the Order', { ...defaultToastOptions, ...toastTypeOptions.info });
      setIsSubmitted(true);
    } catch (err) {
      if (err.response?.status === 422 && err.response?.data?.errors?.length) {
        err.response.data.errors.forEach((error) => {
          toast.error(error, { ...defaultToastOptions, ...toastTypeOptions.info });
        });
      }
    }
  };

  return (
    <div className="loading-dock-request-container">
      <h3 style={{ marginBottom: '20px' }}>Loading Dock Code for - #{order.id}</h3>

      <p>
        Hi <span className="bold">{customer.name}</span>,
      </p>
      <p>
        The delivery personnel requires the loading dock code and/or details for the order #{order.id} to be delivered
        on {order.delivery_at}.
      </p>
      {isSubmitted && (
        <p>
          <strong>Thank you for sending through the loading Dock Code!</strong>
        </p>
      )}
      {!isSubmitted && (
        <>
          <p>Please also attach any relevant documents if necessary.</p>
          <RequestForm order={order} customer={customer} loadingDock={loadingDock} handleSubmit={handleSubmit} />
        </>
      )}
    </div>
  );
};

export default Container;
