import Image from 'next/image';

const FilePreview = ({ fileURL, name }) => {
  const isImage = new RegExp(/\.png|\.jpeg|\.jpg/).test(fileURL);

  if (isImage) {
    return (
      <a href={fileURL} target="_blank" className="loading-dock-preview__image" rel="noopener noreferrer">
        <Image src={fileURL} height={100} width={100} />
      </a>
    );
  }

  return (
    <a className="loading-dock-preview" href={fileURL} target="_blank" rel="noopener noreferrer">
      Attached: {name}
    </a>
  );
};

export default FilePreview;
