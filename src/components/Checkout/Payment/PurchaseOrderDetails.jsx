import { useState, useEffect } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';
import Creatable from 'react-select/creatable';

import { COST_CENTER_ID_FORMATS } from 'utils/constants';

const PurchaseOrderDetails = () => {
  const {
    order,
    orders,
    setOrderDetail,
    setValidationField,
    checkout: {
      purchase_orders: purchaseOrders,
      requires_purchase_order: requiresPO,
      required_department_identity_format: requiredCostCentreIDFormat,
      has_gst_split_invoicing: hasGstSplitInvoicing,
    },
    validations,
  } = useCheckoutStore();

  // check if order has split GST items
  const hasGstSplitItems = () => {
    const checkoutOrder = orders[order.id];
    const orderLines = Object.keys(checkoutOrder.locations).flatMap((locationID) =>
      Object.keys(checkoutOrder.locations[locationID].suppliers).flatMap((supplierID) =>
        Object.values(checkoutOrder.locations[locationID].suppliers[supplierID].order_lines)
      )
    );
    const orderlineGstValues = orderLines.map((orderLine) => orderLine.is_gst_free);
    return orderlineGstValues.includes(true) && orderlineGstValues.includes(false);
  };

  const handleAllowSplitPOs = (canAllow) => {
    setAllowSplitPO(canAllow);
    if (hasGstSplitInvoicing && requiresPO && canAllow) {
      setValidationField('gstFreeCpoId', null);
    } else {
      setValidationField('gstFreeCpoId', null, true);
    }
  };

  const handlePOChange = (PO, gstFree = false) => {
    if (gstFree) {
      setSelectedNonGstPO(PO);
      setOrderDetail('gstFreeCpoId', PO?.value || PO?.label);
    } else {
      setSelectedPO(PO);
      setOrderDetail('cpoId', PO?.value || PO?.label);
    }
  };

  const poOptions = purchaseOrders?.map((PO) => ({
    value: PO.id,
    label: PO.po_number,
    description: PO.description,
    isInactive: PO.inactive,
  }));

  const formatPOOptionLabel = ({ label, isInactive, description }, { context }) => (
    <>
      <div style={{ display: 'flex' }}>
        <div>{label}</div>
        {isInactive && <div style={{ marginLeft: '10px', color: '#ccc' }}>(inactive)</div>}
      </div>
      {context === 'menu' && description && <small style={{ fontSize: '0.8rem' }}>({description})</small>}
    </>
  );

  const [selectedPO, setSelectedPO] = useState(
    order.cpoId ? poOptions.find((option) => option.value === order.cpoId) : ''
  );
  const [selectedNonGstPO, setSelectedNonGstPO] = useState(
    order.gstFreeCpoId ? poOptions.find((option) => option.value === order.gstFreeCpoId) : ''
  );
  const [allowSplitPO, setAllowSplitPO] = useState(order.gstFreeCpoId ? true : null);
  const needsSplitPO = hasGstSplitInvoicing && hasGstSplitItems();

  const costCenterIDFormat = COST_CENTER_ID_FORMATS[requiredCostCentreIDFormat] || {
    type: 'text',
    label: '',
    limit: '',
  };
  const handleDepartmentIDChange = (event) => {
    const { value } = event.target;
    if (!!costCenterIDFormat.limit && value.split('').length > costCenterIDFormat.limit) return;

    setOrderDetail('departmentIdentity', event.target.value);
  };

  // pre-select PO in dropdown if set in Order
  useEffect(() => {
    if (!poOptions?.length || !order?.cpoId) return;
    if (selectedPO?.value === order.cpoId) return;

    setSelectedPO(poOptions.find((option) => option.value === order.cpoId));
  }, [poOptions, order.cpoId]);

  return (
    <>
      {needsSplitPO && allowSplitPO === null && (
        <div className="checkout-detail" style={{ gridColumn: '1/3' }}>
          <p>This order contains a combination of GST-free and GST items.</p>
          <p>
            Would you like to invoice these items separately? <small>(requires split purchase orders)</small>
          </p>
          <a className="button primary checkout-button" onClick={() => handleAllowSplitPOs(true)}>
            YES
          </a>
          <a
            className="button secondary checkout-button"
            style={{ marginLeft: '0.5rem' }}
            onClick={() => handleAllowSplitPOs(false)}
          >
            NO
          </a>
        </div>
      )}
      {needsSplitPO && allowSplitPO && (
        <>
          <div className="checkout-detail">
            <p className="title">PO Number for GST items</p>
            <Creatable
              placeholder="Select or type a new PO number"
              isClearable
              options={poOptions}
              onChange={(PO) => handlePOChange(PO)}
              className={`po-select${validations.cpoId === false ? ' invalid--input' : ''}`}
              noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
              formatOptionLabel={formatPOOptionLabel}
              value={selectedPO}
            />
            {requiresPO && (
              <p>
                <strong>Please note: </strong>
                Your company requires a purchase order number for the GST items of the order.
              </p>
            )}
          </div>
          <div className="checkout-detail">
            <p className="title">PO Number for GST free items</p>
            <Creatable
              placeholder="Select or type a new PO number"
              isClearable
              options={poOptions}
              onChange={(PO) => handlePOChange(PO, true)}
              className={`po-select${validations.gstFreeCpoId === false ? ' invalid--input' : ''}`}
              noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
              formatOptionLabel={formatPOOptionLabel}
              value={selectedNonGstPO}
            />
            {requiresPO && (
              <p>
                <strong>Please note: </strong>
                Your company requires a purchase order number for the GST-free items of the order.
              </p>
            )}
          </div>
        </>
      )}
      {!(needsSplitPO && allowSplitPO) && (
        <div className="checkout-detail">
          <p className="title">PO Number</p>
          <Creatable
            placeholder="Select or type a new PO number"
            isClearable
            options={poOptions}
            onChange={(PO) => handlePOChange(PO)}
            className={`po-select${validations.cpoId === false ? ' invalid--input' : ''}`}
            noOptionsMessage={() => "No saved/active PO's. Type a new PO number."}
            formatOptionLabel={formatPOOptionLabel}
            value={selectedPO}
          />
          {requiresPO && (
            <p>
              <strong>Please note: </strong>
              Your company requires a purchase order number for an order. If you don't have a PO number please enter
              your full name.
            </p>
          )}
        </div>
      )}
      <div className="checkout-detail">
        <p className="title">Cost Centre ID</p>
        <input
          type={costCenterIDFormat.type}
          value={order.departmentIdentity}
          onChange={handleDepartmentIDChange}
          className={`input-icon order checkout-input${!order.departmentIdentity ? ' empty' : ''}${
            validations.departmentIdentity === false ? ' invalid--input' : ''
          }`}
          placeholder="Set A Cost Centre ID"
        />
        {!!requiredCostCentreIDFormat && Object.keys(validations).includes('departmentIdentity') && (
          <p>
            <strong>Please note: </strong>
            Your company requires a {costCenterIDFormat.label} Cost Centre ID for an order.
          </p>
        )}
      </div>
    </>
  );
};

export default PurchaseOrderDetails;
