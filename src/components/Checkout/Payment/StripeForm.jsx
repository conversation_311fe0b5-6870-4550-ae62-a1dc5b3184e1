import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { toast } from 'react-toastify';
import { defaultToastOptions, toastTypeOptions } from 'utils/toasterConfig';

import yordar from 'api/yordar';
import { NEW_STRIPE_CARD_ENDPOINT } from 'api/endpoints';

import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { UserContext } from 'context/user';
import useCheckoutStore from 'store/useCheckoutStore';
import VisaIcon from 'images/icons/visa.svg';
import MasterCardIcon from 'images/icons/mastercard.svg';
import AmexIcon from 'images/icons/amex.svg';
import Image from 'next/image';

const initialCard = {
  id: null,
  name: '',
  stripe_token: null,
  saved_for_future: true,
};

const iconsMap = {
  visa: VisaIcon,
  mastercard: MasterCardIcon,
  amex: AmexIcon,
};

const StripeForm = () => {
  const router = useRouter();
  const { order, setOrderDetail, validations, setValidationField, setInProgress, submitOrder } = useCheckoutStore();
  const { user } = useContext(UserContext);

  const [cardError, setCardError] = useState(null);
  const [creditCard, setCreditCard] = useState(initialCard);
  const [savedCard, setSavedCard] = useState({});

  const [processingState, setProcessingState] = useState(null);

  const stripe = useStripe();
  const elements = useElements();
  const isEditPage = order?.status && order?.status !== 'draft';

  const submitStripeCard = async (e) => {
    setCardError(null);

    if (processingState) return;

    if (!stripe || !elements) {
      setOrderDetail('submitNewCardWithMode', null);
      setInProgress(null);
      return;
    }

    if (!creditCard.name) {
      setCardError('Cardholder Name is required');
      setOrderDetail('submitNewCardWithMode', null);
      setInProgress(null);
      toast.error('New Card Errors detected. Please Check Payment Options.', {
        ...defaultToastOptions,
        ...toastTypeOptions.info,
      });
      return;
    }

    const cardElement = elements.getElement(CardElement);
    setProcessingState('stripe-card-validation');
    try {
      const { paymentMethod: stripePaymentMethod, error: stripeError } = await stripe.createPaymentMethod({
        type: 'card',
        card: cardElement,
        billing_details: {
          name: `${user.first_name} ${user.last_name}`,
          email: user.email,
        },
      });
      if (stripeError) {
        setCardError(stripeError.message);
        setProcessingState(null);
        setOrderDetail('submitNewCardWithMode', null);
        setInProgress(null);
        toast.error('Card Errors detected', { ...defaultToastOptions, ...toastTypeOptions.info });
      } else {
        updateCardFromStripe(stripePaymentMethod);
      }
    } catch (err) {
      setProcessingState(null);
      setCardError('We were unable to connect to our secure card provider. Please try again.');
      setOrderDetail('submitNewCardWithMode', null);
      setInProgress(null);
      toast.error('New Card Errors detected. Please Check Payment Options.', {
        ...defaultToastOptions,
        ...toastTypeOptions.info,
      });
    }
  };

  const handleUpdate = (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    if (event.target.type !== 'checkbox' && event.target.value) {
      setValidationField('creditCardName', true);
    }
    setCreditCard((state) => ({ ...state, [event.target.name]: value }));
  };

  // sets creditCard with details from Stripe (including stripe_token)
  const updateCardFromStripe = (stripePaymentMethod) => {
    const { card: stripeCard } = stripePaymentMethod;
    setCreditCard((state) => ({
      ...state,
      last4: stripeCard.last4,
      brand: stripeCard.brand,
      country_code: stripeCard.country,
      expiry_month: stripeCard.exp_month,
      expiry_year: stripeCard.exp_year,
      stripe_token: stripePaymentMethod.id,
    }));
  };

  useEffect(() => {
    if (order.submitNewCardWithMode) {
      submitStripeCard();
    }
  }, [order.submitNewCardWithMode]);

  useEffect(() => {
    if (!creditCard.stripe_token) return;

    setProcessingState('saving-card');
    saveCardInYordar(creditCard);
  }, [creditCard.stripe_token]);

  const saveCardInYordar = async (card) => {
    const { id, ...sanitizedCard } = card;
    try {
      const { data: yordarCard } = await yordar(NEW_STRIPE_CARD_ENDPOINT, {
        method: 'post',
        withCredentials: true,
        data: {
          credit_card: sanitizedCard,
        },
      });
      setProcessingState(null);
      setSavedCard(yordarCard);

      // reset validations
      setValidationField('creditCardName', null, true);
      setValidationField('creditCardNum', null, true);
      setOrderDetail('creditCardId', yordarCard.id);
      placeOrder();
    } catch (err) {
      setProcessingState(null);
      setOrderDetail('submitNewCardWithMode', null);
      setInProgress(null);
      const responseError = err?.request?.response;
      if (responseError) {
        const { errors } = JSON.parse(responseError);
        setCardError(errors.join('. '));
      } else {
        setCardError('We were unable to save Card! Please try again.');
      }
      toast.error('Card Errors detected', { ...defaultToastOptions, ...toastTypeOptions.info });
    }
  };

  const placeOrder = async () => {
    const submitMode = order.submitNewCardWithMode || '';
    setInProgress(submitMode);
    try {
      const redirectURL = await submitOrder({
        mode: submitMode,
      });
      setOrderDetail('submitNewCardWithMode', null);
      localStorage.removeItem('deliveryDate');
      localStorage.removeItem('deliveryTime');
      if (isEditPage && redirectURL) {
        window.location = redirectURL;
      } else {
        router.push('checkout/success');
      }
    } catch (error) {
      console.log(`Error placing order from Stripe Form: ${error}`);
      setInProgress(null);

      // show error
      toast.dismiss();
      if (error.response) {
        const {
          response: {
            data: { errors: responseErrors },
          },
        } = error;
        responseErrors.forEach((err) => toast.error(`${err}`, { ...defaultToastOptions, ...toastTypeOptions.info }));
      } else if (error.length) {
        for (let i = 0; i < error.length; i += 1) {
          toast.error(error[i].message, { ...defaultToastOptions, ...toastTypeOptions.info });
        }
      } else {
        toast.error(error.message, { ...defaultToastOptions, ...toastTypeOptions.info });
      }
    }
  };

  const stripeCardOptions = {
    hidePostalCode: true,
    style: {
      base: {
        fontSize: '14px',
        fontFamily: 'sans-serif',
        fontSmoothing: 'antialiased',
        color: '#000',
        '::placeholder': {
          color: '#888',
        },
        borderBottom: '1px solid #000',
      },
      invalid: {
        color: '#9e2146',
      },
    },
  };

  if (savedCard.id) {
    return (
      <>
        <h3>Saved Card</h3>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Image src={iconsMap[savedCard.brand]} height={30} width={30} />
          <span style={{ marginLeft: '0.5rem' }}>
            {savedCard.name} ending in {savedCard.last4} (expires {savedCard.expiry_month}/{savedCard.expiry_year})
            {savedCard.saved_for_future && <small> - Saved for Future Use</small>}
          </span>
        </div>
        <div className="notice">
          {savedCard.brand_label} credit card payments will incur a {savedCard.surcharge_percent}%{' '}
          {!!savedCard.surcharge_fee && `+ ${savedCard.surcharge_fee * 100}c surcharge`}.
        </div>
      </>
    );
  }

  return (
    <>
      <input
        id="cardholder-name"
        className={`cardholder-name${validations.creditCardName === false ? ' invalid--input' : ''}`}
        type="text"
        name="name"
        value={creditCard.name}
        onChange={handleUpdate}
        required
        placeholder="Cardholder name"
      />
      <div className={`card-element${validations.creditCardName === false ? ' invalid--input' : ''}`}>
        <CardElement options={stripeCardOptions} />
      </div>
      <label
        className={`filter-checkbox ${creditCard.saved_for_future ? 'checked' : ''}`}
        style={{ marginTop: '16px', fontSize: '16px' }}
      >
        <input type="checkbox" name="saved_for_future" checked={creditCard.saved_for_future} onChange={handleUpdate} />
        Save card for future use
      </label>
      {cardError && (
        <p className="invalid">
          <strong>Error:</strong> {cardError}
        </p>
      )}
      {!!processingState && (
        <div style={{ display: 'flex' }}>
          <button type="button" className="button stripe-submit-button grey">
            {processingState === 'stripe-card-validation' && <span>Validating Card in Stripe...</span>}
            {processingState === 'saving-card' && <span>Saving card against customer...</span>}
          </button>
        </div>
      )}
    </>
  );
};

export default StripeForm;
