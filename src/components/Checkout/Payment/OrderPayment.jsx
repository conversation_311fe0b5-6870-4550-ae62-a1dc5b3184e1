import { useEffect, useState } from 'react';
import SVG from 'react-inlinesvg';

import { PayByNewCard, PayBySavedCard, PayOnAccount, PayOnAccountRequest } from 'components/Checkout';

import CardIcon from 'images/icons/card-filled.svg';
import AccountIcon from 'images/icons/account.svg';
import useCheckoutStore from 'store/useCheckoutStore';

const OrderPayment = () => {
  const {
    setOrderDetail,
    checkout: {
      saved_credit_cards: savedCreditCards,
      can_pay_on_account: canPayOnAccount,
      can_pay_by_credit_card: canPayByCard,
      required_department_identity_format: requiredDepartmentIdentityFormat,
    },
    order: { departmentIdentity, creditCardId },
    setValidationField,
  } = useCheckoutStore();

  let paymentMethods = [
    {
      key: 'pay-on-account',
      label: 'Pay on Account',
      disabled: false, // !canPayOnAccount, // as we have a Pay on Account Request
      icon: AccountIcon,
      weight: 1,
    },
    {
      key: 'pay-by-saved-card',
      label: 'Use A Saved Card',
      disabled: !canPayByCard || !savedCreditCards.length,
      icon: CardIcon,
      hidden: !savedCreditCards.length,
      weight: 2,
    },
    {
      key: 'pay-by-new-card',
      label: 'Use A New Card',
      disabled: !canPayByCard,
      icon: CardIcon,
      weight: 3,
    },
  ];
  if (!canPayOnAccount) {
    // re-set paymentMethod weights
    paymentMethods = paymentMethods.map((paymentMethod) => {
      let methodWeight = paymentMethod.weight;
      if (savedCreditCards.length) {
        methodWeight = paymentMethod.key === 'pay-by-saved-card' ? 1 : 2;
      } else {
        methodWeight = paymentMethod.key === 'pay-by-new-card' ? 1 : 2;
      }
      methodWeight = paymentMethod.key === 'pay-on-account' ? 3 : methodWeight;
      return { ...paymentMethod, weight: methodWeight };
    });
    // re-sort paymentMethod based on new weights
    paymentMethods = paymentMethods.sort((a, b) => a.weight - b.weight);
  }

  const [activePaymentMethod, setActivePaymentMethod] = useState(paymentMethods[0].key);

  const handlePaymentMethodChange = (selectedPaymentMethod) => {
    if (activePaymentMethod === selectedPaymentMethod.key) return;

    if (selectedPaymentMethod.disabled) {
      alert('You Do Not have access to this payment option!');
      return;
    }
    // reset invoice individually
    setOrderDetail('invoiceIndividually', false);

    if (selectedPaymentMethod.key === 'pay-on-account') {
      if (requiredDepartmentIdentityFormat) {
        setValidationField('departmentIdentity', departmentIdentity ? true : null);
      }
      setValidationField('creditCardName', null, true);
      setValidationField('creditCardNum', null, true);
      setOrderDetail('creditCardId', canPayOnAccount ? 1 : null);
    } else if (selectedPaymentMethod.key === 'pay-by-saved-card') {
      setValidationField('departmentIdentity', null, true);
      setValidationField('creditCardName', null, true);
      setValidationField('creditCardNum', null, true);
      setOrderDetail('creditCardId', savedCreditCards[0]?.id);
    } else {
      setValidationField('departmentIdentity', null, true);
      setValidationField('creditCardName', null);
      setValidationField('creditCardNum', null);
      setOrderDetail('creditCardId', null);
    }
    setActivePaymentMethod(selectedPaymentMethod.key);
  };

  useEffect(() => {
    if (creditCardId) {
      let preSelectedMethod = paymentMethods[0].key;

      if (savedCreditCards?.length && creditCardId !== 1) {
        preSelectedMethod = 'pay-by-saved-card';
      } else if (creditCardId === 1) {
        preSelectedMethod = 'pay-on-account';
      }
      setActivePaymentMethod(preSelectedMethod);
    }
  }, [creditCardId, canPayOnAccount]);

  return (
    <div className="checkout-section">
      <h3 className="section-heading heading-block">Payment Options</h3>
      <div>
        <div className="payment-methods">
          {paymentMethods
            .filter((method) => !method.hidden)
            .map((paymentMethod) => (
              <div
                onClick={() => handlePaymentMethodChange(paymentMethod)}
                className={`credit-card${paymentMethod.key === activePaymentMethod ? ' active' : ''} payment-input`}
              >
                <SVG src={paymentMethod.icon.src} width={24} height={24} />
                <p>{paymentMethod.label}</p>
              </div>
            ))}
        </div>

        {activePaymentMethod === 'pay-by-new-card' && <PayByNewCard />}
        {activePaymentMethod === 'pay-by-saved-card' && <PayBySavedCard savedCreditCards={savedCreditCards} />}
        {activePaymentMethod === 'pay-on-account' && canPayOnAccount && <PayOnAccount />}
        {activePaymentMethod === 'pay-on-account' && !canPayOnAccount && <PayOnAccountRequest />}
      </div>
    </div>
  );
};

export default OrderPayment;
