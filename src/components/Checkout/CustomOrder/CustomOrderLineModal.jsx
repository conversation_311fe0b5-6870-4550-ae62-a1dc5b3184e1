import { useState, useEffect } from 'react';
import { Modal } from 'react-responsive-modal';
import 'react-responsive-modal/styles.css';
import useCustomOrderStore from 'store/useCustomOrderStore';
import useCheckoutStore from 'store/useCheckoutStore';

// item images
import { DYNAMIC_CLOUDINARY_PATH } from 'api/endpoints';
import Image from 'next/image';

const categoryOptions = {
  10055: 'Office Catering',
  14: 'Office Snacks & Pantry',
};

const CustomOrderLineModal = () => {
  const { modalOpen, closeModal, currentOrderLine, currentLocation, currentSupplier } = useCustomOrderStore(
    (state) => ({
      modalOpen: state.modalOpen,
      closeModal: state.closeModal,
      currentOrderLine: state.currentOrderLine,
      currentLocation: state.currentLocation,
      currentSupplier: state.currentSupplier,
    })
  );

  const { orders, activeOrderID, addOrderLine, updateOrderLine, orderSuppliers } = useCheckoutStore((state) => ({
    activeOrderID: state.activeOrderID,
    addOrderLine: state.addOrderLine,
    orders: state.orders,
    orderSuppliers: state.orderSuppliers,
    updateOrderLine: state.updateOrderLine,
  }));

  const [loading, setLoading] = useState(false);
  const [localOrderLine, setLocalOrderLine] = useState(currentOrderLine);
  const { custom_item: customItem } = localOrderLine;

  const order = orders[activeOrderID];
  const markup = order?.commission;
  const supplierCommission = currentSupplier?.commission ? parseFloat(currentSupplier.commission) : 0;

  const supplierImageID = orderSuppliers[currentSupplier?.id]?.image_id || currentSupplier?.image;

  useEffect(() => {
    setLocalOrderLine(currentOrderLine);
  }, [currentOrderLine]);

  // trigger price calculations if only baseline exists
  useEffect(() => {
    if (customItem.cost || customItem.price) return;

    const event = { target: { name: 'baseline', value: customItem.baseline } };
    handlePriceChange(event);
  }, [customItem.baseline]);

  const handleOrderLineChange = (event) => {
    const { name, type, value: inputValue } = event.target;
    let value = inputValue;
    if (type === 'number' && inputValue) {
      const numValue = parseInt(value, 10);
      value = numValue >= 1 ? numValue : 1;
    }
    setLocalOrderLine((state) => ({
      ...state,
      [name]: value,
    }));
  };

  const handleItemChange = (event) => {
    const { name, type, checked, value: inputValue } = event.target;
    const value = type === 'checkbox' ? checked : inputValue;

    setLocalOrderLine((state) => ({
      ...state,
      custom_item: {
        ...state.custom_item,
        [name]: value,
      },
    }));
  };

  const handleMarkdownChange = (event) => {
    const baselineIncMarkdown = event.target.checked;
    let priceUpdates = {};
    if (localOrderLine.custom_item.baseline) {
      const baseline = parseFloat(localOrderLine.custom_item.baseline);
      const cost = baselineIncMarkdown ? (baseline * (1 - supplierCommission / 100)).toFixed(2) : baseline;
      priceUpdates = {
        cost,
        price: (cost * (1 + markup / 100)).toFixed(2),
      };
    }

    setLocalOrderLine((state) => ({
      ...state,
      custom_item: {
        ...state.custom_item,
        baseline_inc_markdown: baselineIncMarkdown,
        ...priceUpdates,
      },
    }));
  };

  const handlePriceChange = (event) => {
    const { name, value } = event.target;
    let priceUpdates = {};

    // calculate baseline and baseline with gst
    if (name === 'baseline') {
      const baseline = value === '' ? '' : parseFloat(value) || 0;
      priceUpdates = {
        baseline,
        baseline_inc_gst: baseline === '' ? '' : (baseline * 1.1).toFixed(2),
      };
    } else if (name === 'baseline_inc_gst') {
      const baseline_inc_gst = value === '' ? '' : parseFloat(value) || 0;
      priceUpdates = {
        baseline_inc_gst,
        baseline: baseline_inc_gst === '' ? '' : (baseline_inc_gst / 1.1).toFixed(2),
      };
    }

    // calculate price and cost based on baseline
    const baselineValue = priceUpdates.baseline === '' ? 0 : priceUpdates.baseline;
    const cost = localOrderLine.custom_item.baseline_inc_markdown
      ? (baselineValue * (1 - supplierCommission / 100)).toFixed(2)
      : baselineValue;
    priceUpdates.cost = priceUpdates.baseline === '' ? '' : cost;
    priceUpdates.price = priceUpdates.baseline === '' ? '' : (cost * (1 + markup / 100)).toFixed(2);
    priceUpdates.cost = cost;
    priceUpdates.price = (cost * (1 + markup / 100)).toFixed(2);

    setLocalOrderLine((state) => ({
      ...state,
      custom_item: {
        ...state.custom_item,
        ...priceUpdates,
      },
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!customItem.name || !customItem.baseline || !localOrderLine.quantity || loading) {
      return;
    }

    setLoading(true);

    try {
      if (localOrderLine.id) {
        await updateOrderLine(localOrderLine);
      } else {
        await addOrderLine({ orderLine: localOrderLine, locationID: currentLocation.id });
      }

      closeModal();
    } catch (error) {
      console.error('Error adding custom item:', error);
      // Show user-friendly error message
      alert('There was an error adding the custom item. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const isEditing = !!localOrderLine.id;
  const totalPrice = (parseFloat(customItem.price) * (localOrderLine.quantity || 0)).toFixed(2);
  const buttonDisabled =
    !customItem.name || !customItem.baseline || !localOrderLine.quantity || loading || customItem.baseline === '';
  const itemImageID = customItem.image || localOrderLine.image;

  return (
    <Modal
      open={modalOpen}
      onClose={closeModal}
      center
      className="custom-item-modal"
      styles={{
        modal: {
          minWidth: '500px',
          maxWidth: '1000px',
          padding: 0,
          borderRadius: '12px',
          maxHeight: '75vh',
          overflow: 'auto',
        },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
    >
      <div className="menu-modal">
        <h3 className="menu-modal__heading">{isEditing ? 'Edit Custom Item' : 'Add Custom Item'}</h3>

        <form onSubmit={handleSubmit}>
          <div className="modal-content-grid">
            <div className="custom-item-section">
              {supplierImageID && (
                <div className="supplier-image-banner">
                  <Image
                    src={`${DYNAMIC_CLOUDINARY_PATH({ width: 1000, height: 80 })}/${supplierImageID}`}
                    alt={customItem.name}
                    layout="fill"
                  />
                </div>
              )}
              <h4 style={{ textAlign: 'center' }}>{currentSupplier?.name}</h4>

              {itemImageID && (
                <div className="item-image-banner">
                  <Image
                    src={`${DYNAMIC_CLOUDINARY_PATH({ width: 200, height: 200 })}/${itemImageID}`}
                    alt={customItem.name}
                    width={200}
                    height={200}
                  />
                </div>
              )}

              <label>
                Name *
                <input
                  type="text"
                  name="name"
                  value={customItem.name || localOrderLine.name}
                  onChange={handleItemChange}
                  required
                  placeholder="Enter item name"
                />
              </label>

              <label>
                Description
                <textarea
                  name="description"
                  value={customItem.description}
                  onChange={handleItemChange}
                  placeholder="Enter item description"
                  rows="3"
                />
              </label>

              <label>
                Category
                <select
                  name="category_id"
                  className="checkout-input account"
                  value={localOrderLine.category_id}
                  onChange={handleOrderLineChange}
                  required
                  title="Need a category"
                >
                  <option value="">Select a category</option>
                  {Object.keys(categoryOptions).map((categoryID) => (
                    <option key={`item-${localOrderLine.id}-category-${categoryID}`} value={categoryID}>
                      {categoryOptions[categoryID]}
                    </option>
                  ))}
                </select>
              </label>

              <div className="pricing-section">
                <h5>Pricing</h5>

                <div className="pricing-grid">
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="isGSTFree"
                      name="is_gst_free"
                      checked={customItem.is_gst_free}
                      onChange={handleItemChange}
                      style={{ margin: '0 12px 0 0' }}
                    />
                    <span className="custom-checkbox" />
                    <label htmlFor="isGSTFree">Item is GST Free</label>
                  </div>
                  <div className="checkbox-container">
                    <input
                      type="checkbox"
                      id="baselineIncMarkdown"
                      name="baseline_inc_markdown"
                      checked={customItem.baseline_inc_markdown}
                      onChange={handleMarkdownChange}
                      style={{ margin: '0 12px 0 0' }}
                    />
                    <span className="custom-checkbox" />
                    <label htmlFor="baselineIncMarkdown">Price incl. Supplier Commission ({supplierCommission}%)</label>
                  </div>
                </div>

                <div className="pricing-grid" style={customItem.is_gst_free ? { gridTemplateColumns: '1fr' } : {}}>
                  <label>
                    Price (Exc GST) *
                    <input
                      type="number"
                      step="0.01"
                      min="0"
                      name="baseline"
                      value={customItem.baseline}
                      onChange={handlePriceChange}
                      required
                      placeholder="0.00"
                      style={{ marginBottom: '0' }}
                    />
                  </label>

                  {!customItem.is_gst_free && (
                    <label style={{ marginBottom: 0 }}>
                      Price (Inc GST)
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        name="baseline_inc_gst"
                        value={customItem.baseline_inc_gst}
                        onChange={handlePriceChange}
                        placeholder="0.00"
                      />
                    </label>
                  )}
                </div>

                <div className="pricing-grid">
                  <label>
                    Supplier Price (exc GST)
                    <input
                      type="text"
                      value={customItem.cost}
                      readOnly
                      style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
                    />
                  </label>

                  <label>
                    Customer Price (exc GST) ({markup}%)
                    <input
                      type="text"
                      value={customItem.price}
                      readOnly
                      style={{ backgroundColor: '#f5f5f5', cursor: 'not-allowed' }}
                    />
                  </label>
                </div>

                <label>
                  Quantity *
                  <input
                    type="number"
                    min="1"
                    name="quantity"
                    value={localOrderLine.quantity}
                    onChange={handleOrderLineChange}
                  />
                </label>
              </div>
            </div>
          </div>

          <div className="custom-item-modal-footer">
            <button type="submit" className={`button ${buttonDisabled ? 'disabled' : ''}`} disabled={buttonDisabled}>
              <span>
                {loading
                  ? 'Saving...'
                  : `${isEditing ? 'Update Item' : 'Add to Order'} - $${totalPrice > 0 ? totalPrice : '0.00'}`}
              </span>
            </button>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default CustomOrderLineModal;
