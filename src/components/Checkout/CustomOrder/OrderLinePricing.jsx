// import { useState, useRef, useEffect } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

const OrderLinePricing = ({ orderLine, localOrderLine, setLocalOrderLine, customOrderConfig }) => {
  const { orders, activeOrderID } = useCheckoutStore((state) => ({
    orders: state.orders,
    activeOrderID: state.activeOrderID,
  }));

  // For pricing calculation values
  const order = orders[activeOrderID];
  const markup = order?.commission || 0;
  const currentSupplier = customOrderConfig?.supplier;
  const supplierCommission = currentSupplier?.commission ? parseFloat(currentSupplier.commission) : 0;
  const { custom_item: customItem } = localOrderLine;

  // Handle baseline price changes with pricing calculations
  const handleChange = (event) => {
    const inputValue = event.target.value;

    // Allow empty string for clearing the field
    if (inputValue === '') {
      setLocalOrderLine({
        ...localOrderLine,
        custom_item: {
          ...localOrderLine.custom_item,
          baseline: '',
          baseline_inc_gst: '',
          cost: '0.00',
          price: '0.00',
        },
        order_line_value: '0.00',
      });
      return;
    }
    const baseline = parseFloat(inputValue);

    // Only calculate if we have a valid number
    if (Number.isNaN(baseline)) return;

    // Calculate cost and price based on baseline
    const cost = localOrderLine.custom_item.baseline_inc_markdown
      ? (baseline * (1 - supplierCommission / 100)).toFixed(2)
      : baseline.toFixed(2);
    const price = (cost * (1 + markup / 100)).toFixed(2);
    const baseline_inc_gst = (baseline * 1.1).toFixed(2);

    const updatedOrderLine = {
      ...localOrderLine,
      custom_item: {
        ...localOrderLine.custom_item,
        baseline: inputValue, // Keep the raw input value
        baseline_inc_gst,
        cost,
        price,
      },
      order_line_value: price,
    };

    setLocalOrderLine(updatedOrderLine);
  };

  const handleBlur = async () => {
    // Format the baseline value to 2 decimal places on blur
    const baseline = parseFloat(localOrderLine.custom_item.baseline);
    if (!Number.isNaN(baseline)) {
      const formattedOrderLine = {
        ...localOrderLine,
        custom_item: {
          ...localOrderLine.custom_item,
          baseline: baseline.toFixed(2),
        },
      };
      setLocalOrderLine(formattedOrderLine);
    } else {
      // If invalid, reset to original value
      setLocalOrderLine(orderLine);
    }
  };

  return (
    <div className="custom-orderline-pricing">
      <div className="pricing-fields-row">
        <div className="pricing-field inline">
          <div className="pricing-label" style={{ lineHeight: 1 }}>
            Price (exc GST):
          </div>
          <input
            type="text"
            className="pricing-input"
            value={customItem.baseline}
            onChange={handleChange}
            onBlur={handleBlur}
            placeholder="0.00"
          />
          {!!customItem.baseline_inc_markdown && <sup style={{ marginTop: '-10px', color: 'red' }}>*</sup>}
        </div>
        <div className="pricing-field inline">
          <div className="pricing-label">Supplier:</div>
          <span className="pricing-value">${customItem.cost}</span>
        </div>
        <div className="pricing-field inline">
          <div className="pricing-label">Customer:</div>
          <span className="pricing-value">${customItem.price}</span>
        </div>
      </div>
    </div>
  );
};

export default OrderLinePricing;
