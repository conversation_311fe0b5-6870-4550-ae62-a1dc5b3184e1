import { useState } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

const LocationInput = ({ setShowLocationInput }) => {
  const { activeOrderID, setLocation } = useCheckoutStore((state) => ({
    activeOrderID: state.activeOrderID,
    setLocation: state.setLocation,
  }));
  const [newLocationName, setNewLocationName] = useState('');
  const handleAddLocation = () => {
    const newLocation = newLocationName.trim();
    if (newLocation) {
      setLocation({ orderID: activeOrderID, method: 'post', name: newLocation, isCustomOrder: true });
      setNewLocationName('');
      setShowLocationInput(false);
    }
  };
  return (
    <div style={{ padding: '16px', backgroundColor: '#f8f9fa', borderRadius: '8px' }}>
      <div style={{ display: 'flex', gap: '8px', alignItems: 'center' }}>
        <input
          type="text"
          value={newLocationName}
          onChange={(e) => setNewLocationName(e.target.value)}
          placeholder="Enter section name (e.g., 'Food', 'Staffing', 'Equipment')"
          style={{
            flex: 1,
            padding: '8px 12px',
            border: '1px solid #ddd',
            borderRadius: '4px',
            fontSize: '14px',
          }}
          onKeyPress={(e) => {
            if (e.key === 'Enter') {
              handleAddLocation();
            }
          }}
        />
        <button
          type="button"
          onClick={handleAddLocation}
          disabled={!newLocationName.trim()}
          style={{
            padding: '8px 16px',
            backgroundColor: newLocationName.trim() ? '#1f9e86' : '#ccc',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: newLocationName.trim() ? 'pointer' : 'not-allowed',
          }}
        >
          Add
        </button>
        <button
          type="button"
          onClick={() => {
            setShowLocationInput(false);
            setNewLocationName('');
          }}
          style={{
            padding: '8px 16px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            fontSize: '14px',
            cursor: 'pointer',
          }}
        >
          Cancel
        </button>
      </div>
    </div>
  );
};

export default LocationInput;
