import { useEffect, useState } from 'react';
import Image from 'next/image';
import { Modal } from 'react-responsive-modal';
import yordar from 'api/yordar';

import { DYNAMIC_CLOUDINARY_PATH, DYNAMIC_CUSTOM_ORDER_SUPPLIER_MENU_ENDPOINT } from 'api/endpoints';
import useCustomOrderStore from 'store/useCustomOrderStore';
import useCheckoutStore from 'store/useCheckoutStore';
import { Spinner } from 'components/Common';

const SupplierMenuModal = ({ supplier, location, modalOpen, closeModal }) => {
  const { newOrderLineFromMenu } = useCustomOrderStore((state) => ({
    newOrderLineFromMenu: state.newOrderLineFromMenu,
  }));
  const { orderSuppliers } = useCheckoutStore((state) => ({
    orderSuppliers: state.orderSuppliers,
  }));
  const [menuSupplier, setMenuSupplier] = useState(null);
  const [openSectionId, setOpenSectionId] = useState(null);
  const [openServingsId, setOpenServingsId] = useState(null);
  const [loadingSupplier, setLoadingSupplier] = useState(true);

  const supplierImageID = orderSuppliers[supplier?.id]?.image_id || supplier?.image;

  const fetchSupplierMenu = async () => {
    try {
      const { data } = await yordar.get(DYNAMIC_CUSTOM_ORDER_SUPPLIER_MENU_ENDPOINT({ slug: supplier.slug }), {
        withCredentials: true,
      });
      setMenuSupplier(data);
      setLoadingSupplier(false);
    } catch (err) {
      console.error('Error fetching supplier menu:', err);
    }
  };

  useEffect(() => {
    if (modalOpen) {
      fetchSupplierMenu();
    }
  }, [modalOpen]);

  const handleAddToOrder = (menuItem) => {
    closeModal();
    newOrderLineFromMenu({ menuItem, location, supplier });
  };

  const toggleSection = (id) => {
    setOpenSectionId(openSectionId === id ? null : id);
  };

  return (
    <Modal
      open={modalOpen}
      onClose={closeModal}
      center
      className="custom-item-modal"
      styles={{
        modal: {
          minWidth: '500px',
          maxWidth: '1000px',
          padding: 16,
          borderRadius: '12px',
          maxHeight: '75vh',
          overflow: 'auto',
          ...(loadingSupplier && {
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '300px',
          }),
        },
        closeButton: { cursor: 'pointer', marginLeft: '6px' },
      }}
    >
      {loadingSupplier && <Spinner size="large" />}
      {!loadingSupplier && (
        <div className="custom-item-modal">
          <h3 className="menu-modal__heading">{supplier.name}'s Menu</h3>
          {menuSupplier && (
            <div className="supplier-image-banner">
              <Image
                src={`${DYNAMIC_CLOUDINARY_PATH({ width: 1000, height: 80 })}/${supplierImageID}`}
                alt={supplier.name}
                layout="fill"
              />
            </div>
          )}

          {menuSupplier?.section_grouped_menu_items?.map((section) => (
            <div key={section.id} style={{ borderBottom: '1px solid #ccc', padding: '10px 0' }}>
              <div
                className={`supplier-menu-accordion ${openSectionId === section.id ? 'open' : ''}`}
                onClick={() => toggleSection(section.id)}
              >
                {section.name}
              </div>

              {openSectionId === section.id && (
                <div style={{ marginTop: '10px' }}>
                  {section.menu_items.map((item) => (
                    <>
                      <div
                        key={item.id}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '10px',
                          marginBottom: '10px',
                          cursor: 'pointer',
                        }}
                        onClick={() => {
                          if (item?.serving_sizes?.length > 0) {
                            setOpenServingsId((state) => (state === item.id ? null : item.id));
                          } else {
                            handleAddToOrder(item);
                          }
                        }}
                      >
                        {item.image_id ? (
                          <Image
                            src={`${DYNAMIC_CLOUDINARY_PATH({ width: 80, height: 80 })}/${item.image_id}`}
                            alt={item.name}
                            width={40}
                            height={40}
                          />
                        ) : (
                          <div
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              width: '40px',
                              height: '40px',
                              color: 'white',
                              background: 'black',
                            }}
                          >
                            {item.name.charAt(0).toUpperCase()}
                          </div>
                        )}
                        <div>
                          <div style={{ maxWidth: '400px', fontSize: '14px', lineHeight: '16px' }}>{item.name}</div>
                          <div
                            style={{ fontSize: '14px' }}
                            className={item?.serving_sizes?.length > 0 ? 'has-servings' : ''}
                          >
                            {item?.serving_sizes?.length > 0 ? 'Has Servings' : item.display_price}
                          </div>
                        </div>
                      </div>
                      {openServingsId === item.id && (
                        <div className="serving-sizes">
                          {item.serving_sizes.map((serving) => (
                            <div key={serving.id}>
                              <button
                                type="button"
                                onClick={() =>
                                  handleAddToOrder({
                                    ...serving,
                                    name: `${item.name} - ${serving.name}`,
                                    description: item.description,
                                  })
                                }
                              >
                                {serving.name} - ${serving.price}
                              </button>
                            </div>
                          ))}
                        </div>
                      )}
                    </>
                  ))}
                </div>
              )}
            </div>
          ))}
          {!menuSupplier?.section_grouped_menu_items.length && (
            <p style={{ textAlign: 'center', color: '#666', fontStyle: 'italic', padding: '20px' }}>
              No menu items found for {supplier.name}.
            </p>
          )}
        </div>
      )}
    </Modal>
  );
};

export default SupplierMenuModal;
