const OrderLineQuantity = ({ orderLine, originalQuantity, updateItem }) => {
  const handleChange = ({ byOneAdjustment, inputValue }) => {
    if (byOneAdjustment) {
      return updateItem((state) => ({
        ...state,
        quantity: Number(state.quantity) + byOneAdjustment,
      }));
    }
    if (inputValue || inputValue === '') {
      return updateItem((state) => ({
        ...state,
        prevQuantity: state.quantity,
        quantity: inputValue,
      }));
    }
  };

  // back to original quantity if input is blank on blur
  function handleBlur() {
    if (!orderLine.quantity) {
      return updateItem((state) => ({
        ...state,
        quantity: originalQuantity,
      }));
    }
    return true;
  }

  return (
    <div className="number-input-with-controls">
      <button type="button" onClick={() => handleChange({ byOneAdjustment: -1 })} className="number-control">
        -
      </button>
      <input
        type="number"
        name="quantity"
        value={orderLine.quantity}
        onChange={(e) => handleChange({ inputValue: e.target.value })}
        onBlur={handleBlur}
      />
      <button type="button" onClick={() => handleChange({ byOneAdjustment: 1 })} className="number-control">
        +
      </button>
    </div>
  );
};

export default OrderLineQuantity;
