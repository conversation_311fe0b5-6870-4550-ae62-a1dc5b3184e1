import { useEffect, useState } from 'react';
import { Calendar } from '@hassanmojab/react-modern-calendar-datepicker';
import '@hassanmojab/react-modern-calendar-datepicker/lib/DatePicker.css';

import useCheckoutStore from 'store/useCheckoutStore';
import yordar from 'api/yordar';
import { DYNAMIC_CHECKOUT_DATE_VALIDATION } from 'api/endpoints';
import { TimeSlotList } from 'components/Checkout';
import { ClosureSupplier } from 'components/Common';
import { getDateConfig } from 'utils/dateTime';

const initialErrorState = {
  leadTime: null,
  isOutsideOperatingHours: false,
  isClosed: false,
  closureSuppliers: [],
};

const DeliveryDateModal = ({ setModalOpen, passedDateErrors, isEditPage }) => {
  const {
    order: { id: orderID, deliverySuburbId, deliveryDate, deliveryTime },
    setDate,
  } = useCheckoutStore();

  const [selectedDay, setSelectedDay] = useState(deliveryDate);
  const [selectedTime, setSelectedTime] = useState(deliveryTime || '');
  const [deliveryError, setDeliveryError] = useState(initialErrorState);

  useEffect(() => setDeliveryError(passedDateErrors), [passedDateErrors]);

  function minimumDate() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    // Note: getMonth() returns 0-11, but react-modern-calendar-datepicker expects 1-12 for months
    const currentMonth = currentDate.getMonth() + 1;
    const currentDay = currentDate.getDate();

    return {
      year: currentYear,
      month: currentMonth,
      day: currentDay,
    };
  }

  function clearErrors() {
    setDeliveryError(initialErrorState);
  }

  const submitDeliveryDate = async () => {
    if (!selectedDay || !selectedTime) return;

    // Convert selected day and time into a Date object
    const deliveryDateObject = new Date(
      selectedDay.year,
      selectedDay.month - 1,
      selectedDay.day,
      selectedTime.hours,
      selectedTime.minutes
    );

    const dateConfig = getDateConfig(deliveryDateObject);

    const { data: validationData } = await yordar.get(DYNAMIC_CHECKOUT_DATE_VALIDATION(orderID), {
      params: { order_delivery_at: deliveryDateObject.toISOString(), suburb_id: deliverySuburbId },
      withCredentials: true,
    });

    if (!validationData.can_process_lead_time && !deliveryError.leadTime) {
      setDeliveryError((state) => ({
        ...state,
        leadTime: validationData.formatted_lead_time,
      }));
    } else if (
      (validationData.is_closed || validationData.outside_operating_hours) &&
      !deliveryError.closureSuppliers.length
    ) {
      setDeliveryError((state) => ({
        ...state,
        isOutsideOperatingHours: validationData.outside_operating_hours,
        isClosed: validationData.is_closed,
        closureSuppliers: validationData.supplier_closure_dates,
      }));
    } else {
      setDate({
        deliveryDate: selectedDay,
        deliveryTime: selectedTime,
        deliveryAt: dateConfig.orderFormatted,
        deliveryAtDisplayTime: dateConfig.displayFormatted.time,
        deliveryAtDisplayDate: dateConfig.displayFormatted.date,
      });
      if (!isEditPage) {
        const dateString = `${selectedDay.day}/${selectedDay.month}/${selectedDay.year}`;
        localStorage.setItem('deliveryDate', dateString);
        localStorage.setItem('deliveryTime', selectedTime.display);
      }
      setModalOpen(false);
    }
  };

  return (
    <div>
      <h3>Select Delivery Date & Time</h3>
      <p className="notice">
        Please note: Hot food may arrive up to 15 minutes prior to the delivery time, cold food may arrive up to 60
        minutes prior to the delivery time.
      </p>
      <div className="date-time-container">
        <Calendar
          value={selectedDay}
          onChange={(e) => {
            clearErrors();
            setSelectedDay(e);
          }}
          colorPrimary="#000"
          minimumDate={minimumDate()}
        />
        <TimeSlotList
          onChange={setSelectedTime}
          value={selectedTime.display?.toLowerCase()}
          clearErrors={clearErrors}
        />
      </div>
      {!!deliveryError.leadTime && !deliveryError.closureSuppliers.length && (
        <p className="delivery-date-error">
          Your order requires a minimum lead time of {deliveryError.leadTime}. If you proceed with the order it may be
          rejected by the supplier(s)
        </p>
      )}
      {!!deliveryError.closureSuppliers.length && (
        <>
          {deliveryError.closureSuppliers.map((supplier) => (
            <div key={`supplier-closure-${supplier.id}`}>
              <ClosureSupplier supplier={supplier} />
            </div>
          ))}
          {deliveryError.isClosed && (
            <p className="delivery-date-error">Please place your order before or after these dates.</p>
          )}
          {!deliveryError.isClosed && deliveryError.isOutsideOperatingHours && (
            <p className="delivery-date-error">
              If you proceed the {deliveryError.closureSuppliers.length > 1 ? 'suppliers' : 'supplier'} may reject the
              order.
            </p>
          )}
        </>
      )}
      <button
        type="button"
        onClick={deliveryError.isClosed ? null : submitDeliveryDate}
        className={`button black ${deliveryError.isClosed ? 'disable-date' : ''}`}
        style={{ width: '100%' }}
      >
        {(deliveryError.leadTime || deliveryError.isOutsideOperatingHours) && !deliveryError.isClosed
          ? 'Proceed Anyway'
          : 'Submit'}
      </button>
    </div>
  );
};

export default DeliveryDateModal;
