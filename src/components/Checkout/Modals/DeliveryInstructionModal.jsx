import { useRef, useState, useEffect } from 'react';
import useCheckoutStore from 'store/useCheckoutStore';

const DeliveryInstructionModal = ({ setModalOpen }) => {
  const instructionRef = useRef(null);
  const { order, setOrderDetail } = useCheckoutStore();
  const [deliveryInstructions, setDeliveryInstructions] = useState(order.deliveryInstruction);

  function handleClose() {
    setOrderDetail('deliveryInstruction', deliveryInstructions);
    setModalOpen(false);
  }

  useEffect(() => {
    instructionRef.current.style.height = 'auto';
    const newHeight = instructionRef.current.scrollHeight + 20;
    instructionRef.current.style.height = `${Math.min(350, newHeight)}px`;
  }, [deliveryInstructions]);

  return (
    <div>
      <h3 className="section-heading">Delivery Instructions</h3>
      <textarea
        ref={instructionRef}
        value={deliveryInstructions}
        onChange={(e) => setDeliveryInstructions(e.target.value)}
      />
      <button type="button" className="button black" onClick={() => handleClose()}>
        Save
      </button>
    </div>
  );
};

export default DeliveryInstructionModal;
