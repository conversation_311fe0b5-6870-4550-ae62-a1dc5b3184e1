const LoadingDockHelper = ({ setModalOpen }) => (
  <div>
    <h3 style={{ marginBottom: '12px' }}>Delivery to a Loading Dock</h3>
    <p>
      If your building requires an access code for loading dock deliveries, you can opt in to this delivery method at
      checkout.
      <br />
      Closer to your delivery date, we'll send you an email with a link to provide the necessary access details like
      codes, instructions, or contact info. This ensures a seamless drop-off experience, avoiding delays and helping
      your team receive the order smoothly.
    </p>
    <button type="button" className="button" onClick={() => setModalOpen(false)}>
      Close
    </button>
  </div>
);

export default LoadingDockHelper;
