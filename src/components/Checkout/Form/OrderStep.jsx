const OrderStep = ({ isOrderPanel, orderValidated, setCheckoutPanel, isCustomOrder }) => (
  <div className="order-progress">
    <a className={isOrderPanel ? 'active' : ''} onClick={() => setCheckoutPanel('order')}>
      1. {isCustomOrder ? 'Create Custom Order' : 'Order Summary'}
    </a>
    <a
      className={!isOrderPanel ? 'active' : ''}
      style={!orderValidated ? { cursor: 'not-allowed' } : {}}
      onClick={orderValidated ? () => setCheckoutPanel('details') : null}
    >
      {' '}
      2. Order Details
    </a>
  </div>
);

export default OrderStep;
