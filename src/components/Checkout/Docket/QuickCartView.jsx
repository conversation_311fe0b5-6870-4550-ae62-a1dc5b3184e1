import { DocketSupplier } from 'components/Checkout';
import useCheckoutStore from 'store/useCheckoutStore';

const QuickCartView = ({ checkoutPanel }) => {
  const { order: checkoutOrder, activeOrder } = useCheckoutStore((state) => ({
    order: state.order,
    activeOrder: state.orders[state.activeOrderID],
  }));

  if (!activeOrder) return null;

  const submittedOrderStatuses = ['quoted', 'new', 'saved'];
  if (checkoutPanel === 'success' && checkoutOrder.isCustomOrder) {
    submittedOrderStatuses.push('draft');
  }
  const submmittedOrderID = submittedOrderStatuses.includes(checkoutOrder.status) ? checkoutOrder.id : null;

  // Tally object to store the count for each supplier
  const suppliers = {};
  // Iterate through locations
  Object.values(activeOrder.locations).forEach((location) => {
    // Iterate through suppliers
    Object.values(location.suppliers).forEach((supplier) => {
      suppliers[supplier.id] ||= { id: supplier.id, orderLineCount: 0 };
      suppliers[supplier.id].orderLineCount += Object.values(supplier.order_lines).reduce(
        (sum, orderLine) => sum + orderLine.quantity,
        0
      );
    });
  });

  return (
    <div className="checkout-docket-section" style={{ borderBottom: '2px solid #eaeaea', paddingBottom: '18px' }}>
      <div className="section-heading flex">
        {!checkoutOrder?.mealPlan?.name && (
          <h3>
            {submmittedOrderID && (
              <a href={checkoutOrder.orderLink}>
                Your Order - <span style={{ color: '#1f9e86' }}>#{submmittedOrderID}</span>
              </a>
            )}
            {!submmittedOrderID && 'Your Order'}
          </h3>
        )}
      </div>
      {Object.values(suppliers).map((supplier, index) => (
        <DocketSupplier
          key={`order-${activeOrder.id}-supplier-${supplier.id}`}
          supplier={supplier}
          index={index}
          checkoutOrder={checkoutOrder}
          activeOrder={activeOrder}
          checkoutPanel={checkoutPanel}
        />
      ))}
    </div>
  );
};

export default QuickCartView;
