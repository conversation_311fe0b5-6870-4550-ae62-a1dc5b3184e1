import DebounceInput from 'react-debounce-input';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const MarkupOverride = () => {
  const { orders, activeOrderID, updateOrderCommission } = useCheckoutStore(
    (state) => ({
      orders: state.orders,
      activeOrderID: state.activeOrderID,
      updateOrderCommission: state.updateOrderCommission,
    }),
    shallow
  );

  if (Object.keys(orders).length !== 1) return null;

  const order = orders[activeOrderID];
  const { id: orderId, commission } = order;

  const handleChange = async (event) => {
    const commissionValue = event.target.value.split(' ')[0];
    updateOrderCommission({ orderId, commission: commissionValue });
  };

  const yordarCommission = parseFloat((1 - 1 / (1 + commission / 100)) * 100).toFixed(2);

  return (
    <div className="checkout-detail markup-container" style={{ marginTop: '1rem' }}>
      <label>
        Customer Markup *<small style={{ fontSize: '12px' }}> (must be minimum 25%)</small>
      </label>
      <DebounceInput
        type="text"
        placeholder="Markup Override - defaults to supplier Markup(s)"
        value={commission}
        className="checkout-input input-icon order markup-input"
        onChange={handleChange}
        debounceTimeout={500}
        readOnly={!activeOrderID}
      />
      <p>
        Yordar Commission {yordarCommission}%
        {yordarCommission < 20 && <small style={{ fontSize: '12px', color: 'red' }}> (must be minimum 20%)</small>}
      </p>
    </div>
  );
};

export default MarkupOverride;
