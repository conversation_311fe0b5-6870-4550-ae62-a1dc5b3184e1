import useCheckoutStore from 'store/useCheckoutStore';

const DocketDays = () => {
  const { orders, activeOrderID, setActiveOrderID } = useCheckoutStore();

  return (
    <div className="recurring-day">
      {Object.values(orders).map((recurringOrder) => (
        <a
          style={{ textTransform: 'uppercase', flex: 1 }}
          className={activeOrderID === recurringOrder.id ? 'active' : ''}
          onClick={() => setActiveOrderID(recurringOrder.id)}
        >
          {recurringOrder.name}
        </a>
      ))}
    </div>
  );
};

export default DocketDays;
