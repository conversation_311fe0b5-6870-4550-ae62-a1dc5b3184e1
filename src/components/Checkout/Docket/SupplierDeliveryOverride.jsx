import yordar from 'api/yordar';
import DebounceInput from 'react-debounce-input';
import { DYNAMIC_UPDATE_ORDER_ENDPOINT } from 'api/endpoints';
import useCheckoutStore from 'store/useCheckoutStore';
import { shallow } from 'zustand/shallow';

const SupplierDeliveryOverride = ({ activeOrder, activeOrderSupplier }) => {
  const { updateOrderSupplier } = useCheckoutStore(
    (state) => ({
      updateOrderSupplier: state.updateOrderSupplier,
    }),
    shallow
  );

  const handleChange = async (event) => {
    const deliveryFeeOverride = event.target.value;
    try {
      const {
        data: { totals },
      } = await yordar.put(DYNAMIC_UPDATE_ORDER_ENDPOINT(activeOrder.id), {
        order: {
          order_supplier: {
            id: activeOrderSupplier.id,
            supplier_profile_id: activeOrderSupplier.supplier_profile_id,
            delivery_fee_override: deliveryFeeOverride,
          },
        },
      });
      updateOrderSupplier({
        orderID: activeOrder.id,
        orderSupplier: {
          ...activeOrderSupplier,
          delivery_fee_override: deliveryFeeOverride,
        },
        totals,
      });
    } catch (err) {
      // do nothing
    }
  };

  return (
    <div className="checkout-detail" style={{ marginTop: '1rem' }}>
      <DebounceInput
        type="text"
        placeholder={`Delivery Fee Override${
          'default_delivery_fee' in activeOrderSupplier
            ? ` (default: $${activeOrderSupplier.default_delivery_fee})`
            : ''
        }`}
        value={activeOrderSupplier.delivery_fee_override}
        className="checkout-input input-icon delivery"
        onChange={handleChange}
        debounceTimeout={500}
      />
    </div>
  );
};

export default SupplierDeliveryOverride;
