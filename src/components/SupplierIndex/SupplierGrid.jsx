import { memo } from 'react';
import { useRouter } from 'next/router';

import { Link } from 'components/Common';
import { SupplierDetails, SupplierCard } from 'components/SupplierIndex';

const Supplier = memo(function Supplier({ supplier }) {
  const router = useRouter();
  const { mealUUID, quoteUUID } = router.query;

  let supplierLink = `/show/${supplier.slug}`;
  if (mealUUID) {
    supplierLink += `?mealUUID=${mealUUID}`;
  } else if (quoteUUID) {
    supplierLink += `?quoteUUID=${quoteUUID}`;
  }

  return (
    <div className="listing-grid-item">
      <Link href={supplierLink} className="listing-grid-item__link" />
      <SupplierCard listing={supplier} />
      <SupplierDetails supplier={supplier} />
    </div>
  );
});
const SupplierGrid = ({ suppliers }) => (
  <section className="listing-grid">
    <section className="listing-grid-items">
      {suppliers.map((supplier) => (
        <Supplier supplier={supplier} key={supplier.id} />
      ))}
    </section>
  </section>
);

export default SupplierGrid;
